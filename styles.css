/* CSS Custom Properties for Theme Management */
:root {
    /* Light Mode Colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --bg-accent: #f1f3f4;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-tertiary: #adb5bd;
    --border-primary: #dee2e6;
    --border-secondary: #e9ecef;
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-heavy: rgba(0, 0, 0, 0.15);
    --accent-primary: #495057;
    --accent-secondary: #6c757d;
    --hover-bg: #f8f9fa;
    --active-bg: #e9ecef;
}

[data-theme="dark"] {
    /* Dark Mode Colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --bg-accent: #333333;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-tertiary: #808080;
    --border-primary: #404040;
    --border-secondary: #333333;
    --shadow-light: rgba(0, 0, 0, 0.2);
    --shadow-medium: rgba(0, 0, 0, 0.3);
    --shadow-heavy: rgba(0, 0, 0, 0.4);
    --accent-primary: #cccccc;
    --accent-secondary: #999999;
    --hover-bg: #333333;
    --active-bg: #404040;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--bg-primary);
    min-height: 100vh;
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header Styles */
.app-header {
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-primary);
    padding: 0.5rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 1px 4px var(--shadow-light);
    transition: all 0.3s ease;
}

.header-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-section .material-icons {
    font-size: 1.75rem;
    color: var(--accent-primary);
}

.logo-section h1 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.header-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Header Icon Buttons */
.header-icon-btn {
    background: none;
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 0.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: var(--text-secondary);
    width: 40px;
    height: 40px;
}

.header-icon-btn:hover {
    background-color: var(--hover-bg);
    border-color: var(--accent-secondary);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.header-icon-btn .material-icons {
    font-size: 1.2rem;
}

.theme-icon {
    font-size: 1.2rem;
}

.header-actions .mdc-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.header-actions .mdc-button--outlined {
    border-color: var(--border-primary);
    color: var(--text-primary);
}

.header-actions .mdc-button--outlined:hover {
    background-color: var(--hover-bg);
    border-color: var(--accent-secondary);
}

.header-actions .mdc-button--raised {
    background-color: var(--accent-primary);
    color: var(--bg-primary);
    border: none;
}

.header-actions .mdc-button--raised:hover {
    background-color: var(--text-primary);
}

/* Main Content */
.main-content {
    max-width: 1600px;
    margin: 0 auto;
    padding: 0rem 10rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Upload Section */
.upload-section {
    background-color: var(--bg-secondary);
    border-radius: 6px;
    padding: 1rem;
    box-shadow: 0 1px 4px var(--shadow-light);
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.upload-container {
    max-width: 100%;
}

/* Tab Navigation */
.upload-tabs {
    display: flex;
    background-color: var(--bg-tertiary);
    border-radius: 4px;
    padding: 2px;
    margin-bottom: 1rem;
}

.tab-button {
    flex: 1;
    background: none;
    border: none;
    padding: 0.5rem 0.75rem;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;
    font-weight: 500;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.tab-button.active {
    background-color: var(--accent-primary);
    color: var(--bg-primary);
    box-shadow: 0 2px 4px var(--shadow-medium);
}

.tab-button:hover:not(.active) {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--border-primary);
    border-radius: 6px;
    padding: 1.5rem 1rem;
    text-align: center;
    background-color: var(--bg-accent);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--accent-secondary);
    background-color: var(--hover-bg);
    transform: translateY(-1px);
}

.upload-area.dragover {
    border-color: var(--text-primary);
    background-color: var(--active-bg);
}

.upload-icon .material-icons {
    font-size: 3rem;
    color: var(--accent-primary);
    margin-bottom: 1rem;
}

.upload-area h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.upload-area p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
}

/* Upload Options */
.upload-options {
    margin-top: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.template-download {
    display: flex;
    justify-content: center;
}

.template-download .mdc-button {
    min-width: 200px;
}

/* Modern Form Styling */
.manual-entry-form {
    background-color: var(--bg-primary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-primary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.25rem;
    margin-bottom: 1.5rem;
}

.form-grid .full-width {
    grid-column: 1 / -1;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.full-width-field {
    grid-column: 1 / -1;
    margin-top: 1rem;
}

.field-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    letter-spacing: 0.025em;
}

.input-wrapper,
.select-wrapper,
.date-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.modern-input,
.modern-select,
.modern-date,
.modern-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-primary);
    border-radius: 8px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-family: inherit;
    transition: all 0.3s ease;
    outline: none;
}

.modern-input:focus,
.modern-select:focus,
.modern-date:focus,
.modern-textarea:focus {
    border-color: var(--text-secondary);
    box-shadow: 0 0 0 3px rgba(128, 128, 128, 0.1);
    transform: translateY(-1px);
}

.modern-input::placeholder,
.modern-textarea::placeholder {
    color: var(--text-tertiary);
    font-style: italic;
}

.modern-textarea {
    resize: vertical;
    /* min-height: 80px; */
    line-height: 1.4;
}

.select-wrapper {
    position: relative;
}

.modern-select {
    appearance: none;
    cursor: pointer;
    padding-right: 3rem;
}

.select-arrow {
    position: absolute;
    right: 1rem;
    pointer-events: none;
    color: var(--text-secondary);
    transition: transform 0.3s ease;
}

.select-wrapper:hover .select-arrow {
    transform: scale(1.1);
}

.date-wrapper {
    position: relative;
}

.modern-date {
    padding-right: 3rem;
    cursor: pointer;
}

.date-icon {
    position: absolute;
    right: 1rem;
    pointer-events: none;
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.modern-date::-webkit-calendar-picker-indicator {
    opacity: 0;
    position: absolute;
    right: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

/* Form Validation Error Styles */
.field-error .modern-input,
.field-error .modern-select,
.field-error .modern-date,
.field-error .modern-textarea {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
}

.field-error .modern-input:focus,
.field-error .modern-select:focus,
.field-error .modern-date:focus,
.field-error .modern-textarea:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2) !important;
}

.field-error .field-label {
    color: #dc3545 !important;
}

/* Dark mode error styles */
[data-theme="dark"] .field-error .modern-input,
[data-theme="dark"] .field-error .modern-select,
[data-theme="dark"] .field-error .modern-date,
[data-theme="dark"] .field-error .modern-textarea {
    border-color: #ff6b6b !important;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1) !important;
}

[data-theme="dark"] .field-error .modern-input:focus,
[data-theme="dark"] .field-error .modern-select:focus,
[data-theme="dark"] .field-error .modern-date:focus,
[data-theme="dark"] .field-error .modern-textarea:focus {
    border-color: #ff6b6b !important;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.2) !important;
}

[data-theme="dark"] .field-error .field-label {
    color: #ff6b6b !important;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-secondary);
    flex-wrap: wrap;
}

/* Pending Entries Section */
.pending-entries {
    margin-top: 1.5rem;
    background-color: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-primary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.pending-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.pending-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.pending-actions {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.pending-list {
    display: grid;
    gap: 0.75rem;
}

.pending-item {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-secondary);
    border-radius: 6px;
    padding: 1rem;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 1rem;
    align-items: center;
    transition: all 0.3s ease;
}

.pending-item:hover {
    border-color: var(--accent-secondary);
    box-shadow: 0 2px 8px var(--shadow-light);
}

.pending-item-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
    font-size: 0.85rem;
}

.pending-item-field {
    display: flex;
    flex-direction: column;
    gap: 0.1rem;
}

.pending-item-field .label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.pending-item-field .value {
    color: var(--text-primary);
    font-weight: 500;
}

.pending-item-actions {
    display: flex;
    gap: 0.5rem;
}

.pending-item-actions .mdc-icon-button {
    width: 32px;
    height: 32px;
    padding: 4px;
}

/* Material Design Component Customizations */
.mdc-text-field--outlined {
    border-radius: 6px;
}

.mdc-text-field--outlined .mdc-notched-outline__leading,
.mdc-text-field--outlined .mdc-notched-outline__trailing {
    border-radius: 6px;
}

.mdc-text-field--outlined .mdc-notched-outline__leading,
.mdc-text-field--outlined .mdc-notched-outline__notch,
.mdc-text-field--outlined .mdc-notched-outline__trailing {
    border-color: var(--border-primary);
}

.mdc-text-field--outlined:hover .mdc-notched-outline__leading,
.mdc-text-field--outlined:hover .mdc-notched-outline__notch,
.mdc-text-field--outlined:hover .mdc-notched-outline__trailing {
    border-color: var(--accent-secondary);
}

.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__leading,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__notch,
.mdc-text-field--outlined.mdc-text-field--focused .mdc-notched-outline__trailing {
    border-color: var(--accent-primary);
}

.mdc-text-field__input {
    color: var(--text-primary);
}

.mdc-floating-label {
    color: var(--text-secondary);
}

.mdc-floating-label--float-above {
    color: var(--accent-primary);
}

.mdc-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 500;
}

.mdc-button--raised {
    background-color: var(--accent-primary);
    color: var(--bg-primary);
    box-shadow: 0 2px 4px var(--shadow-medium);
}

.mdc-button--raised:hover {
    background-color: var(--text-primary);
}

.mdc-select--outlined .mdc-notched-outline__leading,
.mdc-select--outlined .mdc-notched-outline__notch,
.mdc-select--outlined .mdc-notched-outline__trailing {
    border-color: var(--border-primary);
}

.mdc-select__selected-text {
    color: var(--text-primary);
}

.mdc-select__dropdown-icon {
    fill: var(--text-secondary);
}

/* Contracts Section */
.contracts-section {
    background-color: var(--bg-secondary);
    border-radius: 6px;
    padding: 1rem;
    box-shadow: 0 1px 4px var(--shadow-light);
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.section-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.search-filter {
    min-width: 280px;
}

/* Table Styles */
.contracts-table-container {
    overflow-x: auto;
    border-radius: 6px;
    box-shadow: 0 1px 4px var(--shadow-light);
    border: 1px solid var(--border-primary);
    width: 100%;
    max-width: 100%;
}

.contracts-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-primary);
    border-radius: 6px;
    overflow: hidden;
    table-layout: fixed;
}

.contracts-table th {
    background-color: var(--accent-primary);
    color: var(--bg-primary);
    padding: 1rem 0.3rem;
    text-align: left;
    font-weight: 600;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.2px;
    border-bottom: 1px solid var(--border-primary);
    line-height: 1.2;
}

.contracts-table td {
    padding: 0.35rem 0.3rem;
    border-bottom: 1px solid var(--border-secondary);
    vertical-align: middle;
    color: var(--text-primary);
    font-size: 0.75rem;
    line-height: 1.3;
}

/* Percentage-based column widths for proper container fitting */
.contracts-table th:nth-child(1),
.contracts-table td:nth-child(1) {
    width: 12%;
    text-align: center;
}

.contracts-table th:nth-child(2),
.contracts-table td:nth-child(2) {
    width: 15%;
    text-align: center;
}

.contracts-table th:nth-child(3),
.contracts-table td:nth-child(3) {
    width: 25%;
    text-align: center;
}

.contracts-table th:nth-child(4),
.contracts-table td:nth-child(4) {
    width: 25%;
    text-align: center;
}

.contracts-table th:nth-child(5),
.contracts-table td:nth-child(5) {
    width: 23%;
    text-align: center;
}

.contracts-table tbody tr {
    transition: all 0.3s ease;
}

.contracts-table tbody tr:hover {
    background-color: var(--hover-bg);
}

.contracts-table tbody tr:last-child td {
    border-bottom: none;
}

/* Table content alignment and overflow handling */
.contracts-table td,
.contracts-table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.contracts-table .mdc-icon-button {
    padding: 4px;
    width: 32px;
    height: 32px;
}

/* Icon Buttons */
.mdc-icon-button {
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-radius: 4px;
}

.mdc-icon-button:hover {
    color: var(--text-primary);
    background-color: var(--hover-bg);
}

/* Tablet Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
    .contracts-table-container {
        width: 100%;
    }

    /* Tablet maintains percentage-based widths */
    .contracts-table th:nth-child(1),
    .contracts-table td:nth-child(1) {
        width: 12%;
    }

    .contracts-table th:nth-child(2),
    .contracts-table td:nth-child(2) {
        width: 15%;
    }

    .contracts-table th:nth-child(3),
    .contracts-table td:nth-child(3) {
        width: 25%;
    }

    .contracts-table th:nth-child(4),
    .contracts-table td:nth-child(4) {
        width: 25%;
    }

    .contracts-table th:nth-child(5),
    .contracts-table td:nth-child(5) {
        width: 23%;
    }
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 0 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .upload-section,
    .contracts-section {
        padding: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .date-range {
        grid-template-columns: 1fr;
    }

    .manual-entry-form {
        padding: 1rem;
    }

    .modern-input,
    .modern-select,
    .modern-date,
    .modern-textarea {
        padding: 0.6rem 0.8rem;
        font-size: 0.85rem;
    }

    .pending-entries {
        padding: 1rem;
    }

    .pending-header {
        flex-direction: column;
        align-items: stretch;
    }

    .pending-actions {
        justify-content: stretch;
    }

    .pending-actions .mdc-button {
        flex: 1;
    }

    .pending-item {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .pending-item-details {
        grid-template-columns: 1fr;
    }

    .pending-item-actions {
        justify-content: center;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .search-filter {
        min-width: auto;
    }

    .upload-tabs {
        flex-direction: column;
    }

    .tab-button {
        justify-content: flex-start;
    }

    .contracts-table th {
        padding: 0.8rem 0.25rem;
        font-size: 0.7rem;
    }

    .contracts-table td {
        padding: 0.3rem 0.25rem;
        font-size: 0.7rem;
    }

    .contracts-table {
        width: 100%;
    }

    .contracts-table-container {
        width: 100%;
        margin: 0;
    }

    /* Mobile column widths - percentage-based for proper fitting */
    .contracts-table th:nth-child(1),
    .contracts-table td:nth-child(1) {
        width: 12%;
    }

    .contracts-table th:nth-child(2),
    .contracts-table td:nth-child(2) {
        width: 15%;
    }

    .contracts-table th:nth-child(3),
    .contracts-table td:nth-child(3) {
        width: 25%;
    }

    .contracts-table th:nth-child(4),
    .contracts-table td:nth-child(4) {
        width: 25%;
    }

    .contracts-table th:nth-child(5),
    .contracts-table td:nth-child(5) {
        width: 23%;
    }

    .header-actions {
        gap: 0.3rem;
    }

    .header-icon-btn {
        width: 36px;
        height: 36px;
        padding: 0.4rem;
    }

    .header-icon-btn .material-icons {
        font-size: 1rem;
    }

    .section-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
    }

    .search-filter {
        min-width: auto;
    }

    .modal-content {
        width: 95%;
        max-height: 95vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }

    .contract-details-grid {
        grid-template-columns: 1fr;
    }

    .modal-footer {
        flex-direction: column;
    }

    /* Mobile modal adjustments */
    .highlighted-info {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }

    .highlight-value {
        font-size: 0.9rem;
        padding: 0.4rem 0;
        padding-left: 0.4rem;
    }

    /* Mobile part info row */
    .part-info-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
}

/* Dark Mode Enhancements */
[data-theme="dark"] {
    /* Enhanced dark mode colors for better contrast */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2d2d2d;
    --bg-accent: #252525;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --border-primary: #404040;
    --border-secondary: #2d2d2d;
    --shadow-light: rgba(0, 0, 0, 0.3);
    --shadow-medium: rgba(0, 0, 0, 0.4);
    --shadow-heavy: rgba(0, 0, 0, 0.6);
    --accent-primary: #e0e0e0;
    --accent-secondary: #b3b3b3;
    --hover-bg: #2d2d2d;
    --active-bg: #404040;
}

/* Input Field Enhancements for Dark Mode */
[data-theme="dark"] .mdc-text-field__input {
    color: var(--text-primary) !important;
    caret-color: var(--text-primary) !important;
}

[data-theme="dark"] .mdc-text-field__input::placeholder {
    color: var(--text-tertiary) !important;
}

[data-theme="dark"] .mdc-floating-label {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .mdc-floating-label--float-above {
    color: var(--accent-primary) !important;
}

[data-theme="dark"] .mdc-notched-outline__leading,
[data-theme="dark"] .mdc-notched-outline__notch,
[data-theme="dark"] .mdc-notched-outline__trailing {
    border-color: var(--border-primary) !important;
}

[data-theme="dark"] .mdc-text-field--focused .mdc-notched-outline__leading,
[data-theme="dark"] .mdc-text-field--focused .mdc-notched-outline__notch,
[data-theme="dark"] .mdc-text-field--focused .mdc-notched-outline__trailing {
    border-color: var(--accent-primary) !important;
}

[data-theme="dark"] .mdc-select__selected-text {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .mdc-select__dropdown-icon {
    fill: var(--text-secondary) !important;
}

[data-theme="dark"] .mdc-menu-surface {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
}

[data-theme="dark"] .mdc-list-item {
    color: var(--text-primary) !important;
}

/* Modal Dark Mode Enhancements */
[data-theme="dark"] .modal-overlay {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Modern Form Dark Mode */
[data-theme="dark"] .modern-input,
[data-theme="dark"] .modern-select,
[data-theme="dark"] .modern-date,
[data-theme="dark"] .modern-textarea {
    background-color: var(--bg-secondary) !important;
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
}

[data-theme="dark"] .modern-input:focus,
[data-theme="dark"] .modern-select:focus,
[data-theme="dark"] .modern-date:focus,
[data-theme="dark"] .modern-textarea:focus {
    border-color: var(--text-secondary) !important;
    box-shadow: 0 0 0 3px rgba(224, 224, 224, 0.1) !important;
}

[data-theme="dark"] .modern-input::placeholder,
[data-theme="dark"] .modern-textarea::placeholder {
    color: var(--text-tertiary) !important;
}

[data-theme="dark"] .field-label {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .date-icon,
[data-theme="dark"] .select-arrow {
    color: var(--text-secondary) !important;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(10px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-secondary);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error States */
.success {
    border-color: var(--text-primary) !important;
    background-color: var(--active-bg) !important;
}

.error {
    border-color: var(--text-secondary) !important;
    background-color: var(--hover-bg) !important;
}

/* Enhanced Notification Styles */
.notification {
    position: fixed;
    top: 80px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    border: 1px solid;
    min-width: 300px;
    max-width: 400px;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.notification.show {
    transform: translateX(0);
}

.notification::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

/* Light Mode Notifications */
.notification.success {
    background-color: #f0f9f0;
    color: #2d5a2d;
    border-color: #4caf50;
}

.notification.success::before {
    background-color: #4caf50;
    content: '✓';
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.notification.error {
    background-color: #fdf2f2;
    color: #7d2d2d;
    border-color: #f44336;
}

.notification.error::before {
    background-color: #f44336;
    content: '✕';
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.notification.info {
    background-color: #f0f4f8;
    color: #2d4a5a;
    border-color: #2196f3;
}

.notification.info::before {
    background-color: #2196f3;
    content: 'i';
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Dark Mode Notifications */
[data-theme="dark"] .notification.success {
    background-color: #1a2e1a;
    color: #90ee90;
    border-color: #4caf50;
}

[data-theme="dark"] .notification.error {
    background-color: #2e1a1a;
    color: #ffb3b3;
    border-color: #f44336;
}

[data-theme="dark"] .notification.info {
    background-color: #1a1e2e;
    color: #87ceeb;
    border-color: #2196f3;
}

/* Search Input Customization */
.mdc-text-field--with-leading-icon .mdc-text-field__icon--leading {
    color: var(--text-secondary);
}

.mdc-text-field__input::placeholder {
    color: var(--text-tertiary);
}

/* Menu and List Customizations */
.mdc-menu-surface {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-primary);
    box-shadow: 0 4px 12px var(--shadow-medium);
}

.mdc-list-item {
    color: var(--text-primary);
}

.mdc-list-item:hover {
    background-color: var(--hover-bg);
}

.mdc-list-item--selected {
    background-color: var(--active-bg);
    color: var(--accent-primary);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: 8px;
    box-shadow: 0 8px 32px var(--shadow-heavy);
    border: 1px solid var(--border-primary);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.modal-header-actions .mdc-button {
    border-radius: 6px;
    text-transform: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.modal-icon-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.4rem;
    border-radius: 3px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-icon-btn:hover {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.4rem;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: var(--hover-bg);
    color: var(--text-primary);
}

.modal-body {
    padding: 1rem;
}

/* Highlighted Company and Branch Information */
.highlighted-info {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.highlight-item {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.highlight-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.highlight-value {
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    padding: 0.5rem 0;
    border-bottom: 2px solid var(--accent-primary);
    background-color: var(--bg-primary);
    padding-left: 0.5rem;
    border-radius: 3px;
}

.contract-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.2rem;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

/* Part Information Row - Custom Layout */
.part-info-row {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    margin-bottom: 0.75rem;
}

.part-info-row .detail-item {
    margin-bottom: 0;
}

.part-number {
    min-width: 200px;
}

.part-description {
    flex: 1;
}

.detail-item label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

.detail-item span {
    color: var(--text-primary);
    font-size: 0.9rem;
    padding: 0.4rem 0;
    border-bottom: 1px solid var(--border-secondary);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    padding: 1rem;
    border-top: 1px solid var(--border-primary);
    background-color: var(--bg-secondary);
}

/* Enhanced Button Styling */
.mdc-button {
    border-radius: 6px !important;
    text-transform: none !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease !important;
}

.mdc-button--outlined {
    border-color: var(--border-primary) !important;
    color: var(--text-primary) !important;
    background-color: transparent !important;
}

.mdc-button--outlined:hover {
    background-color: var(--hover-bg) !important;
    border-color: var(--accent-secondary) !important;
}

.mdc-button--raised {
    background-color: var(--accent-primary) !important;
    color: var(--bg-primary) !important;
    border: none !important;
    box-shadow: 0 2px 4px var(--shadow-medium) !important;
}

.mdc-button--raised:hover {
    background-color: var(--text-primary) !important;
    box-shadow: 0 4px 8px var(--shadow-heavy) !important;
}

.mdc-button .material-icons {
    margin-right: 0.5rem !important;
    font-size: 1rem !important;
}
